import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Exam, Question, Course, CreateQuestionDto, AnswerOption, QuestionType } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const ExamQuestionsManager: React.FC = () => {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  
  const [exam, setExam] = useState<Exam | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<CreateQuestionDto>({
    examId: 0,
    courseId: 0,
    questionNumber: 1,
    questionText: '',
    options: ['', '', '', ''],
    correctAnswer: AnswerOption.A,
    type: QuestionType.MULTIPLE_CHOICE,
  });

  useEffect(() => {
    const fetchData = async () => {
      if (!examId) {
        setError('شناسه آزمون نامعتبر است');
        setIsLoading(false);
        return;
      }

      try {
        const [examData, questionsData, coursesData] = await Promise.all([
          apiService.getExam(parseInt(examId)),
          apiService.getQuestionsByExam(parseInt(examId)),
          apiService.getCourses(),
        ]);

        setExam(examData);
        setQuestions(questionsData.sort((a, b) => a.questionNumber - b.questionNumber));
        setCourses(coursesData);
        
        // Set form exam ID
        setFormData(prev => ({ ...prev, examId: examData.id }));
      } catch (err: any) {
        setError(err.response?.data?.message || 'خطا در بارگذاری اطلاعات');
        console.error('Error fetching data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [examId]);

  const resetForm = () => {
    const nextQuestionNumber = questions.length > 0 ? Math.max(...questions.map(q => q.questionNumber)) + 1 : 1;
    setFormData({
      examId: exam?.id || 0,
      courseId: 0,
      questionNumber: nextQuestionNumber,
      questionText: '',
      options: ['', '', '', ''],
      correctAnswer: AnswerOption.A,
      type: QuestionType.MULTIPLE_CHOICE,
    });
    setEditingQuestion(null);
  };

  const handleAddQuestion = () => {
    resetForm();
    setShowAddForm(true);
  };

  const handleEditQuestion = (question: Question) => {
    setFormData({
      examId: question.examId,
      courseId: question.courseId,
      questionNumber: question.questionNumber,
      questionText: question.questionText,
      options: [...question.options],
      correctAnswer: question.correctAnswer,
      type: question.type,
    });
    setEditingQuestion(question);
    setShowAddForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.questionText.trim() || formData.courseId === 0) {
      setError('لطفاً تمام فیلدهای ضروری را پر کنید');
      return;
    }

    if (formData.options.some(option => !option.trim())) {
      setError('لطفاً تمام گزینه‌ها را پر کنید');
      return;
    }

    try {
      if (editingQuestion) {
        const updatedQuestion = await apiService.updateQuestion(editingQuestion.id, formData);
        setQuestions(prev => prev.map(q => q.id === editingQuestion.id ? updatedQuestion : q));
      } else {
        const newQuestion = await apiService.createQuestion(formData);
        setQuestions(prev => [...prev, newQuestion].sort((a, b) => a.questionNumber - b.questionNumber));
      }
      
      setShowAddForm(false);
      resetForm();
      setError('');
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ذخیره سوال');
    }
  };

  const handleDeleteQuestion = async (questionId: number) => {
    if (!window.confirm('آیا از حذف این سوال اطمینان دارید؟')) {
      return;
    }

    try {
      await apiService.deleteQuestion(questionId);
      setQuestions(prev => prev.filter(q => q.id !== questionId));
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در حذف سوال');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (!exam) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">{error || 'آزمون یافت نشد'}</p>
        <button
          onClick={() => navigate('/admin/exams')}
          className="mt-4 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded"
        >
          بازگشت به لیست آزمون‌ها
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-white">مدیریت سوالات آزمون</h1>
          <button
            onClick={() => navigate('/admin/exams')}
            className="bg-dark-600 hover:bg-dark-500 text-white px-4 py-2 rounded transition-colors"
          >
            بازگشت
          </button>
        </div>
        
        <div className="bg-dark-800 p-4 rounded-lg mb-6">
          <h2 className="text-xl font-semibold text-white mb-2">اطلاعات آزمون</h2>
          <p className="text-dark-300"><strong>نام آزمون:</strong> {exam.name}</p>
          <p className="text-dark-300"><strong>تعداد سوالات:</strong> {convertDigitsToPersian(questions.length)}</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Add Question Button */}
      <div className="mb-6">
        <button
          onClick={handleAddQuestion}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded font-medium transition-colors"
        >
          + افزودن سوال جدید
        </button>
      </div>

      {/* Add/Edit Question Form */}
      {showAddForm && (
        <div className="bg-dark-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            {editingQuestion ? 'ویرایش سوال' : 'افزودن سوال جدید'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-medium mb-2">شماره سوال</label>
                <input
                  type="number"
                  min="1"
                  value={formData.questionNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, questionNumber: parseInt(e.target.value) || 1 }))}
                  className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded text-white focus:outline-none focus:border-primary-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-white font-medium mb-2">درس</label>
                <select
                  value={formData.courseId}
                  onChange={(e) => setFormData(prev => ({ ...prev, courseId: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded text-white focus:outline-none focus:border-primary-500"
                  required
                >
                  <option value={0}>انتخاب درس</option>
                  {courses.map(course => (
                    <option key={course.id} value={course.id}>
                      {course.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-white font-medium mb-2">متن سوال</label>
              <textarea
                value={formData.questionText}
                onChange={(e) => setFormData(prev => ({ ...prev, questionText: e.target.value }))}
                className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded text-white focus:outline-none focus:border-primary-500"
                rows={3}
                required
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">گزینه‌ها</label>
              <div className="space-y-2">
                {formData.options.map((option, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <span className="text-white font-medium w-8">
                      {Object.values(AnswerOption)[index]})
                    </span>
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => {
                        const newOptions = [...formData.options];
                        newOptions[index] = e.target.value;
                        setFormData(prev => ({ ...prev, options: newOptions }));
                      }}
                      className="flex-1 px-3 py-2 bg-dark-600 border border-dark-500 rounded text-white focus:outline-none focus:border-primary-500"
                      placeholder={`گزینه ${Object.values(AnswerOption)[index]}`}
                      required
                    />
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-white font-medium mb-2">پاسخ صحیح</label>
              <select
                value={formData.correctAnswer}
                onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value as AnswerOption }))}
                className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded text-white focus:outline-none focus:border-primary-500"
                required
              >
                {Object.values(AnswerOption).map(option => (
                  <option key={option} value={option}>
                    گزینه {option}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex gap-4">
              <button
                type="submit"
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded font-medium transition-colors"
              >
                {editingQuestion ? 'به‌روزرسانی سوال' : 'افزودن سوال'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  resetForm();
                  setError('');
                }}
                className="bg-dark-600 hover:bg-dark-500 text-white px-6 py-2 rounded font-medium transition-colors"
              >
                انصراف
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Questions List */}
      <div className="bg-dark-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-dark-700">
          <h3 className="text-xl font-semibold text-white">لیست سوالات</h3>
        </div>
        
        {questions.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-dark-300">هیچ سوالی برای این آزمون تعریف نشده است</p>
          </div>
        ) : (
          <div className="divide-y divide-dark-700">
            {questions.map((question) => (
              <div key={question.id} className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h4 className="text-white font-medium">
                      سوال {convertDigitsToPersian(question.questionNumber)} - {question.course?.name}
                    </h4>
                    <p className="text-dark-300 mt-2">{question.questionText}</p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleEditQuestion(question)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                      ویرایش
                    </button>
                    <button
                      onClick={() => handleDeleteQuestion(question.id)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                      حذف
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {question.options.map((option, index) => (
                    <div
                      key={index}
                      className={`p-2 rounded text-sm ${
                        Object.values(AnswerOption)[index] === question.correctAnswer
                          ? 'bg-green-600/20 border border-green-500 text-green-200'
                          : 'bg-dark-700 text-dark-300'
                      }`}
                    >
                      <strong>{Object.values(AnswerOption)[index]})</strong> {option}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamQuestionsManager;
