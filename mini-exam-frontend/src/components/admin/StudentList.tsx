import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { User, Exam } from '../../types';
import apiService from '../../services/api';

const StudentList: React.FC = () => {
  const [students, setStudents] = useState<User[]>([]);
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [usersData, examsData] = await Promise.all([
          apiService.getUsers(),
          apiService.getExams(),
        ]);
        
        // Filter only students (users with USER role)
        // Filter only students (users who are not admins)
        const studentsOnly = usersData.filter(user => !user.isAdmin);
        setStudents(studentsOnly);
        setExams(examsData);
      } catch (err) {
        setError('خطا در دریافت اطلاعات.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm('آیا از حذف این دانش‌آموز اطمینان دارید؟')) {
      try {
        await apiService.deleteUser(id);
        setStudents(students.filter((student) => student.id !== id));
      } catch (err) {
        setError('خطا در حذف دانش‌آموز.');
        console.error(err);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری دانش‌آموزان...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white">مدیریت دانش‌آموزان</h1>
        <Link 
          to="/admin/students/new" 
          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          افزودن دانش‌آموز جدید
        </Link>
      </div>

      {students.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-dark-300 text-lg">دانش‌آموزی یافت نشد.</p>
          <Link 
            to="/admin/students/new" 
            className="inline-block mt-4 bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            افزودن اولین دانش‌آموز
          </Link>
        </div>
      ) : (
        <div className="bg-dark-800 rounded-lg overflow-hidden shadow-lg">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-dark-700">
                <tr>
                  <th className="py-3 px-6 text-left text-xs font-medium text-dark-300 uppercase tracking-wider">شناسه</th>
                  <th className="py-3 px-6 text-left text-xs font-medium text-dark-300 uppercase tracking-wider">نام کاربری</th>
                  <th className="py-3 px-6 text-left text-xs font-medium text-dark-300 uppercase tracking-wider">نام</th>
                  <th className="py-3 px-6 text-left text-xs font-medium text-dark-300 uppercase tracking-wider">نام خانوادگی</th>
                  <th className="py-3 px-6 text-left text-xs font-medium text-dark-300 uppercase tracking-wider">تاریخ ایجاد</th>
                  <th className="py-3 px-6 text-left text-xs font-medium text-dark-300 uppercase tracking-wider">عملیات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-700">
                {students.map((student) => (
                  <tr key={student.id} className="hover:bg-dark-700/50">
                    <td className="py-4 px-6 text-white">{student.id}</td>
                    <td className="py-4 px-6 text-white font-medium">{student.username}</td>
                    <td className="py-4 px-6 text-dark-300">{student.name || '-'}</td>
                    <td className="py-4 px-6 text-dark-300">{student.lastName || '-'}</td>
                    <td className="py-4 px-6 text-dark-300">
                      {new Date(student.createdAt).toLocaleDateString()}
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex space-x-2">
                        <Link 
                          to={`/admin/students/edit/${student.id}`} 
                          className="text-blue-400 hover:text-blue-300 text-sm font-medium"
                        >
                          ویرایش
                        </Link>
                        
                        {exams.length > 0 && (
                          <div className="relative group">
                            <button className="text-green-400 hover:text-green-300 text-sm font-medium">
                              شرکت در آزمون
                            </button>
                            <div className="absolute right-0 mt-2 w-48 bg-dark-800 border border-dark-600 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                              <div className="py-1">
                                {exams.map((exam) => (
                                  <Link
                                    key={exam.id}
                                    to={`/admin/exam-session/${exam.id}/student/${student.id}`}
                                    className="block px-4 py-2 text-sm text-dark-300 hover:bg-dark-700 hover:text-white"
                                  >
                                    {exam.name}
                                  </Link>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                        
                        <button
                          onClick={() => handleDelete(student.id)}
                          className="text-red-400 hover:text-red-300 text-sm font-medium"
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentList;
