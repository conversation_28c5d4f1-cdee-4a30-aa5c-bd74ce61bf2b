import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { User } from '../../types';
import apiService from '../../services/api';
import { generateRandomUsername, generateRandomPassword } from '../../utils/persianUtils';
import ImageUpload from '../common/ImageUpload';

interface StudentFormProps {
  isEdit?: boolean;
}

const StudentForm: React.FC<StudentFormProps> = ({ isEdit = false }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<Partial<User>>({
    username: '',
    password: '',
    name: '',
    lastName: '',
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [generatedCredentials, setGeneratedCredentials] = useState<{username: string, password: string} | null>(null);
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null);

  useEffect(() => {
    if (isEdit && id) {
      setLoading(true);
      apiService.getUser(parseInt(id))
        .then((user) => {
          setFormData({
            username: user.username,
            name: user.name,
            lastName: user.lastName,
            // Password is not pre-filled for security reasons
          });

          // Set profile image URL if exists
          if (user.profileImage) {
            const fullImageUrl = `${process.env.REACT_APP_API_URL || 'http://localhost:4000'}/uploads/profile-images/${user.profileImage}`;
            setProfileImageUrl(fullImageUrl);
          }
        })
        .catch((err) => {
          setError('خطا در دریافت اطلاعات دانش‌آموز.');
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [isEdit, id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const generateCredentials = () => {
    const username = generateRandomUsername(formData.name, formData.lastName);
    const password = generateRandomPassword();
    
    setFormData(prev => ({
      ...prev,
      username,
      password,
    }));
    
    setGeneratedCredentials({ username, password });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (isEdit && id) {
        const dataToSend = { ...formData };
        if (!dataToSend.password) {
          delete dataToSend.password;
        }
        await apiService.updateUser(parseInt(id), dataToSend);
        setSuccess('دانش‌آموز با موفقیت به‌روزرسانی شد!');
      } else {
        // For new students, generate credentials if not provided
        if (!formData.username || !formData.password) {
          const username = formData.username || generateRandomUsername(formData.name, formData.lastName);
          const password = formData.password || generateRandomPassword();
          
          setFormData(prev => ({
            ...prev,
            username,
            password,
          }));
          
          setGeneratedCredentials({ username, password });
        }
        
        await apiService.createUser(formData);
        setSuccess('دانش‌آموز با موفقیت ایجاد شد!');
        
        if (!isEdit) {
          // Clear form after creation but keep generated credentials visible
          setFormData({
            username: '',
            password: '',
            name: '',
            lastName: '',
          });
        }
      }
      
      // Redirect after a short delay to show success message
      setTimeout(() => {
        navigate('/admin/students');
      }, 2000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ذخیره دانش‌آموز.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEdit) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری اطلاعات دانش‌آموز...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-3xl font-bold text-white mb-6">
        {isEdit ? 'ویرایش دانش‌آموز' : 'افزودن دانش‌آموز جدید'}
      </h1>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {generatedCredentials && (
        <div className="bg-blue-900/50 border border-blue-500 text-blue-200 px-4 py-3 rounded mb-4">
          <h3 className="font-bold mb-2">اطلاعات کاربری ایجاد شده:</h3>
          <p><strong>نام کاربری:</strong> {generatedCredentials.username}</p>
          <p><strong>گذرواژه:</strong> {generatedCredentials.password}</p>
          <p className="text-sm mt-2 text-blue-300">لطفاً این اطلاعات را ذخیره کرده و در اختیار دانش‌آموز قرار دهید.</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-dark-800 p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="name" className="block text-dark-300 text-sm font-bold mb-2">
              نام:
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
              required
            />
          </div>
          <div>
            <label htmlFor="lastName" className="block text-dark-300 text-sm font-bold mb-2">
              نام خانوادگی:
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName || ''}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
              required
            />
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="username" className="block text-dark-300 text-sm font-bold mb-2">
            نام کاربری:
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username || ''}
              onChange={handleChange}
              className="flex-1 px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
              placeholder="برای ایجاد خودکار، خالی بگذارید"
            />
            {!isEdit && (
              <button
                type="button"
                onClick={generateCredentials}
                className="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded transition-colors"
              >
                ایجاد
              </button>
            )}
          </div>
        </div>

        <div className="mb-6">
          <label htmlFor="password" className="block text-dark-300 text-sm font-bold mb-2">
            گذرواژه: {isEdit && <span className="text-dark-400 text-xs">(برای عدم تغییر، خالی بگذارید)</span>}
          </label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password || ''}
            onChange={handleChange}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
            placeholder={isEdit ? "برای عدم تغییر، خالی بگذارید" : "برای ایجاد خودکار، خالی بگذارید"}
            {...(!isEdit && !formData.password && { required: false })}
          />
        </div>

        <div className="mb-6">
          <label className="block text-dark-300 text-sm font-bold mb-2">
            تصویر پروفایل:
          </label>
          <ImageUpload
            userId={isEdit && id ? parseInt(id) : undefined}
            currentImageUrl={profileImageUrl || undefined}
            onImageUploaded={(imageUrl) => {
              setProfileImageUrl(imageUrl);
              setSuccess('تصویر پروفایل با موفقیت آپلود شد!');
            }}
            onError={(error) => {
              setError(error);
            }}
            disabled={loading}
            className="max-w-sm"
          />
        </div>

        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={() => navigate('/admin/students')}
            className="bg-dark-600 hover:bg-dark-500 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            انصراف
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-800 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            {loading ? 'در حال ذخیره...' : (isEdit ? 'به‌روزرسانی دانش‌آموز' : 'ایجاد دانش‌آموز')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default StudentForm;
