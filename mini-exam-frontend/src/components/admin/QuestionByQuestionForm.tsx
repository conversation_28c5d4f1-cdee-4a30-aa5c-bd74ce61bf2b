import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Exam, Question, User, CreateSessionByQuestionsDto, QuestionAnswerDto, AnswerOption } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const QuestionByQuestionForm: React.FC = () => {
  const { examId, studentId } = useParams<{ examId: string; studentId: string }>();
  const navigate = useNavigate();
  
  const [exam, setExam] = useState<Exam | null>(null);
  const [student, setStudent] = useState<User | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [answers, setAnswers] = useState<Map<number, string>>(new Map());
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      if (!examId || !studentId) {
        setError('شناسه آزمون یا دانشجو نامعتبر است');
        setIsLoading(false);
        return;
      }

      try {
        const [examData, studentData, questionsData] = await Promise.all([
          apiService.getExamWithDetails(parseInt(examId)),
          apiService.getUser(parseInt(studentId)),
          apiService.getQuestionsByExam(parseInt(examId)),
        ]);

        // Verify student is actually a student (not an ADMIN)
        if (studentData.isAdmin) {
          setError('کاربر انتخاب شده دانشجو نیست');
          setIsLoading(false);
          return;
        }

        if (questionsData.length === 0) {
          setError('هیچ سوالی برای این آزمون تعریف نشده است');
          setIsLoading(false);
          return;
        }

        setExam(examData);
        setStudent(studentData);
        setQuestions(questionsData.sort((a, b) => a.questionNumber - b.questionNumber));
      } catch (err: any) {
        setError(err.response?.data?.message || 'خطا در بارگذاری اطلاعات');
        console.error('Error fetching data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [examId, studentId]);

  const handleAnswerChange = (questionId: number, answer: string) => {
    const newAnswers = new Map(answers);
    if (answer === '') {
      newAnswers.delete(questionId);
    } else {
      newAnswers.set(questionId, answer);
    }
    setAnswers(newAnswers);
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleGoToQuestion = (index: number) => {
    setCurrentQuestionIndex(index);
  };

  const handleSubmit = async () => {
    if (!exam || !student) return;

    try {
      setIsSubmitting(true);
      
      const questionAnswers: QuestionAnswerDto[] = questions.map(question => ({
        questionId: question.id,
        selectedAnswer: answers.get(question.id) || undefined,
      }));

      const sessionData: CreateSessionByQuestionsDto = {
        examId: exam.id,
        questionAnswers: questionAnswers,
      };

      // Create session for the specific student
      const result = await apiService.createSessionByQuestionsForUser(student.id, sessionData);
      navigate(`/admin/session-result/${result.id}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ثبت پاسخ‌ها');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (!exam || !student || questions.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">{error || 'آزمون، دانشجو یا سوالات یافت نشد'}</p>
        <button
          onClick={() => navigate('/admin/sessions/create')}
          className="mt-4 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded"
        >
          بازگشت
        </button>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const answeredCount = answers.size;
  const totalQuestions = questions.length;

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-white">ثبت پاسخ‌های آزمون</h1>
          <button
            onClick={() => navigate('/admin/sessions/create')}
            className="bg-dark-600 hover:bg-dark-500 text-white px-4 py-2 rounded transition-colors"
          >
            بازگشت
          </button>
        </div>
        
        <div className="bg-dark-800 p-4 rounded-lg mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h2 className="text-lg font-semibold text-white mb-2">اطلاعات آزمون</h2>
              <p className="text-dark-300"><strong>نام آزمون:</strong> {exam.name}</p>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-white mb-2">اطلاعات دانشجو</h2>
              <p className="text-dark-300"><strong>نام:</strong> {student.name} {student.lastName}</p>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-white mb-2">پیشرفت</h2>
              <p className="text-dark-300">
                <strong>پاسخ داده شده:</strong> {convertDigitsToPersian(answeredCount)} از {convertDigitsToPersian(totalQuestions)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Question Navigation */}
      <div className="bg-dark-800 rounded-lg p-4 mb-6">
        <h3 className="text-white font-medium mb-3">انتخاب سوال:</h3>
        <div className="grid grid-cols-10 gap-2">
          {questions.map((question, index) => (
            <button
              key={question.id}
              onClick={() => handleGoToQuestion(index)}
              className={`w-10 h-10 rounded text-sm font-medium transition-colors ${
                index === currentQuestionIndex
                  ? 'bg-primary-600 text-white'
                  : answers.has(question.id)
                  ? 'bg-green-600 text-white'
                  : 'bg-dark-600 text-dark-300 hover:bg-dark-500'
              }`}
            >
              {convertDigitsToPersian(question.questionNumber)}
            </button>
          ))}
        </div>
      </div>

      {/* Current Question */}
      <div className="bg-dark-800 rounded-lg p-6 mb-6">
        <div className="mb-4">
          <h2 className="text-xl font-bold text-white mb-2">
            سوال {convertDigitsToPersian(currentQuestion.questionNumber)}
          </h2>
          <p className="text-dark-300 text-sm mb-4">
            درس: {currentQuestion.course?.name || 'نامشخص'}
          </p>
        </div>

        <div className="mb-6">
          <p className="text-white text-lg leading-relaxed">
            {currentQuestion.questionText}
          </p>
        </div>

        <div className="space-y-3">
          {currentQuestion.options.map((option, index) => {
            const optionLetter = Object.values(AnswerOption)[index];
            const isSelected = answers.get(currentQuestion.id) === optionLetter;
            
            return (
              <label
                key={index}
                className={`flex items-center p-4 rounded-lg border cursor-pointer transition-colors ${
                  isSelected
                    ? 'bg-primary-600/20 border-primary-500'
                    : 'bg-dark-700 border-dark-600 hover:bg-dark-600'
                }`}
              >
                <input
                  type="radio"
                  name={`question-${currentQuestion.id}`}
                  value={optionLetter}
                  checked={isSelected}
                  onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                  className="sr-only"
                />
                <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ml-3 ${
                  isSelected ? 'border-primary-500 bg-primary-500' : 'border-dark-400'
                }`}>
                  {isSelected && <div className="w-2 h-2 bg-white rounded-full"></div>}
                </div>
                <span className="text-white font-medium ml-2">{optionLetter})</span>
                <span className="text-white">{option}</span>
              </label>
            );
          })}
          
          {/* No Answer Option */}
          <label
            className={`flex items-center p-4 rounded-lg border cursor-pointer transition-colors ${
              !answers.has(currentQuestion.id)
                ? 'bg-yellow-600/20 border-yellow-500'
                : 'bg-dark-700 border-dark-600 hover:bg-dark-600'
            }`}
          >
            <input
              type="radio"
              name={`question-${currentQuestion.id}`}
              value=""
              checked={!answers.has(currentQuestion.id)}
              onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
              className="sr-only"
            />
            <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ml-3 ${
              !answers.has(currentQuestion.id) ? 'border-yellow-500 bg-yellow-500' : 'border-dark-400'
            }`}>
              {!answers.has(currentQuestion.id) && <div className="w-2 h-2 bg-white rounded-full"></div>}
            </div>
            <span className="text-yellow-400 font-medium">بدون پاسخ</span>
          </label>
        </div>
      </div>

      {/* Navigation and Submit */}
      <div className="flex justify-between items-center">
        <button
          onClick={handlePrevious}
          disabled={currentQuestionIndex === 0}
          className="bg-dark-600 hover:bg-dark-500 disabled:bg-dark-700 disabled:cursor-not-allowed text-white px-6 py-2 rounded transition-colors"
        >
          سوال قبلی
        </button>

        <div className="flex gap-4">
          {currentQuestionIndex === questions.length - 1 ? (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white font-bold py-2 px-8 rounded transition-colors"
            >
              {isSubmitting ? 'در حال ثبت...' : 'ثبت نهایی پاسخ‌ها'}
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded transition-colors"
            >
              سوال بعدی
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionByQuestionForm;
