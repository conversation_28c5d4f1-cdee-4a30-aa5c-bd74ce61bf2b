import React, { useState, useRef, useCallback, useEffect } from 'react';
import apiService from '../../services/api';

interface ImageUploadProps {
  userId?: number;
  currentImageUrl?: string;
  onImageUploaded?: (imageUrl: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

interface ValidationError {
  field: string;
  message: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  userId,
  currentImageUrl,
  onImageUploaded,
  onError,
  disabled = false,
  className = '',
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const [dragActive, setDragActive] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Effect to update previewUrl when currentImageUrl changes from props
  useEffect(() => {
    setPreviewUrl(currentImageUrl || null);
  }, [currentImageUrl]);

  // Effect to clean up blob URLs when previewUrl changes or component unmounts
  useEffect(() => {
    let oldBlobUrl: string | null = null;
    // Store the current previewUrl if it's a blob URL
    if (previewUrl && previewUrl.startsWith('blob:')) {
      oldBlobUrl = previewUrl;
    }

    return () => {
      // Revoke the old blob URL when the component unmounts or previewUrl changes
      if (oldBlobUrl) {
        URL.revokeObjectURL(oldBlobUrl);
      }
    };
  }, [previewUrl]);

  const validateImage = (file: File): ValidationError[] => {
    const errors: ValidationError[] = [];
    
    // Check file type
    if (!file.type.match(/^image\/(jpeg|jpg|png|gif)$/)) {
      errors.push({
        field: 'format',
        message: 'فقط تصاویر با فرمت JPEG، PNG و GIF مجاز هستند'
      });
    }

    // Check file size (5MB limit for upload, will be processed to 70KB)
    if (file.size > 5 * 1024 * 1024) {
      errors.push({
        field: 'size',
        message: 'حجم تصویر باید کمتر از 5 مگابایت باشد'
      });
    }

    return errors;
  };

  const checkImageDimensions = (file: File): Promise<ValidationError[]> => {
    return new Promise((resolve) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        const errors: ValidationError[] = [];
        const aspectRatio = img.width / img.height;
        
        // Check aspect ratio (3:4 or 4:6 with 5% tolerance)
        const validRatios = [3/4, 4/6];
        const tolerance = 0.05;
        const isValidRatio = validRatios.some(ratio => 
          Math.abs(aspectRatio - ratio) <= tolerance
        );
        
        // if (!isValidRatio) {
        //   errors.push({
        //     field: 'aspectRatio',
        //     message: 'نسبت ابعاد تصویر باید 3:4 یا 4:6 (عمودی) باشد'
        //   });
        // }
        
        // resolve(errors);
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        resolve([{
          field: 'format',
          message: 'فایل تصویر نامعتبر است'
        }]);
      };
      
      img.src = url;
    });
  };

  const handleFileUpload = useCallback(async (file: File) => {
    if (!userId) {
      onError?.('شناسه کاربری برای آپلود تصویر الزامی است');
      return;
    }

    setValidationErrors([]);
    
    // Basic validation
    const basicErrors = validateImage(file);
    if (basicErrors.length > 0) {
      setValidationErrors(basicErrors);
      return;
    }

    // Dimension validation
    const dimensionErrors = await checkImageDimensions(file);
    if (dimensionErrors.length > 0) {
      setValidationErrors(dimensionErrors);
      return;
    }

    setIsUploading(true);
    
    // Create preview immediately
    const tempPreviewUrl = URL.createObjectURL(file);
    setPreviewUrl(tempPreviewUrl); // Set temporary blob URL for immediate feedback
    
    try {
      // Upload image
      const result = await apiService.uploadProfileImage(userId, file);
      
      // Set the actual uploaded image URL from the server
      const fullImageUrl = `${process.env.REACT_APP_API_URL || 'http://localhost:4000'}${result.imageUrl}`;
      setPreviewUrl(fullImageUrl); // Update to final URL
      
      onImageUploaded?.(fullImageUrl);
      
    } catch (error: any) {
      // Revert to currentImageUrl on error
      setPreviewUrl(currentImageUrl || null); 
      
      const errorMessage = error.response?.data?.message || 'خطا در آپلود تصویر';
      onError?.(errorMessage);
      setValidationErrors([{
        field: 'upload',
        message: errorMessage
      }]);
    } finally {
      setIsUploading(false);
    }
  }, [userId, currentImageUrl, onImageUploaded, onError]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (disabled || isUploading) return;
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileUpload(files[0]);
    }
  }, [disabled, isUploading, handleFileUpload]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileUpload(files[0]);
    }
  }, [handleFileUpload]);

  const handleClick = useCallback(() => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  }, [disabled, isUploading]);

  return (
    <div className={`relative ${className}`}>
      <div
        className={`
          border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors
          ${dragActive ? 'border-primary-400 bg-primary-50 dark:bg-primary-900/20' : 'border-dark-600'}
          ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary-400'}
          ${validationErrors.length > 0 ? 'border-red-400' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/gif"
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled || isUploading}
        />
        
        {previewUrl ? (
          <div className="space-y-2">
            <img
              src={previewUrl}
              alt="Profile preview"
              className="mx-auto h-32 w-24 object-cover rounded-lg border border-dark-600"
            />
            {!isUploading && (
              <p className="text-sm text-dark-300">
                برای تغییر تصویر کلیک کنید یا بکشید
              </p>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="mx-auto h-32 w-24 bg-dark-700 rounded-lg flex items-center justify-center">
              <svg className="h-8 w-8 text-dark-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <p className="text-sm text-dark-300">
              تصویر را اینجا بکشید یا کلیک کنید
            </p>
          </div>
        )}
        
        {isUploading && (
          <div className="absolute inset-0 bg-dark-800/80 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
              <p className="mt-2 text-sm text-white">در حال پردازش تصویر...</p>
            </div>
          </div>
        )}
      </div>
      
      {validationErrors.length > 0 && (
        <div className="mt-2 space-y-1">
          {validationErrors.map((error, index) => (
            <p key={index} className="text-sm text-red-400">
              {error.message}
            </p>
          ))}
        </div>
      )}
      
      <div className="mt-2 text-xs text-dark-400">
        <p>• جهت‌گیری عمودی (نسبت ابعاد 3:4 یا 4:6)</p>
        <p>• فرمت JPEG توصیه می‌شود</p>
        <p>• تصویر به طور خودکار تغییر اندازه و بهینه‌سازی خواهد شد</p>
      </div>
    </div>
  );
};

export default ImageUpload;
