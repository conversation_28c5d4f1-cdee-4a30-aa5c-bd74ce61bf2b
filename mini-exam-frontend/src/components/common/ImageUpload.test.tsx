import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ImageUpload from './ImageUpload';
import apiService from '../../services/api';

// Mock the API service
jest.mock('../../services/api', () => ({
  uploadProfileImage: jest.fn(),
}));

// Mock URL.createObjectURL and URL.revokeObjectURL
global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');
global.URL.revokeObjectURL = jest.fn();

// Mock Image constructor
global.Image = class {
  onload: (() => void) | null = null;
  onerror: (() => void) | null = null;
  src: string = '';
  width: number = 300;
  height: number = 400;

  constructor() {
    setTimeout(() => {
      if (this.onload) {
        this.onload();
      }
    }, 100);
  }
} as any;

describe('ImageUpload', () => {
  const mockOnImageUploaded = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders upload area correctly', () => {
    render(
      <ImageUpload
        userId={1}
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    expect(screen.getByText('Click or drag image here')).toBeInTheDocument();
    expect(screen.getByText('• Portrait orientation (3:4 or 4:6 aspect ratio)')).toBeInTheDocument();
    expect(screen.getByText('• JPEG format recommended')).toBeInTheDocument();
  });

  it('displays current image when provided', () => {
    const currentImageUrl = 'http://localhost:4000/uploads/profile-images/test.jpg';
    
    render(
      <ImageUpload
        userId={1}
        currentImageUrl={currentImageUrl}
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    const image = screen.getByAltText('Profile preview');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', currentImageUrl);
  });

  it('shows validation error for invalid file type', async () => {
    render(
      <ImageUpload
        userId={1}
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    const fileInput = screen.getByRole('button').querySelector('input[type="file"]') as HTMLInputElement;
    
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    fireEvent.change(fileInput, { target: { files: [invalidFile] } });

    await waitFor(() => {
      expect(screen.getByText('Only JPEG, PNG, and GIF images are allowed')).toBeInTheDocument();
    });
  });

  it('shows validation error for file too large', async () => {
    render(
      <ImageUpload
        userId={1}
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    const fileInput = screen.getByRole('button').querySelector('input[type="file"]') as HTMLInputElement;
    
    // Create a file larger than 5MB
    const largeFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });
    
    fireEvent.change(fileInput, { target: { files: [largeFile] } });

    await waitFor(() => {
      expect(screen.getByText('Image must be smaller than 5MB')).toBeInTheDocument();
    });
  });

  it('calls onError when userId is not provided', async () => {
    render(
      <ImageUpload
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    const fileInput = screen.getByRole('button').querySelector('input[type="file"]') as HTMLInputElement;
    
    const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    fireEvent.change(fileInput, { target: { files: [validFile] } });

    await waitFor(() => {
      expect(mockOnError).toHaveBeenCalledWith('User ID is required for image upload');
    });
  });

  it('successfully uploads valid image', async () => {
    const mockApiResponse = {
      message: 'Profile image uploaded successfully',
      imageUrl: '/uploads/profile-images/user_1_123456789.jpg',
    };

    (apiService.uploadProfileImage as jest.Mock).mockResolvedValue(mockApiResponse);

    render(
      <ImageUpload
        userId={1}
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    const fileInput = screen.getByRole('button').querySelector('input[type="file"]') as HTMLInputElement;
    
    const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    fireEvent.change(fileInput, { target: { files: [validFile] } });

    await waitFor(() => {
      expect(screen.getByText('Processing image...')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(apiService.uploadProfileImage).toHaveBeenCalledWith(1, validFile);
      expect(mockOnImageUploaded).toHaveBeenCalledWith(
        'http://localhost:4000/uploads/profile-images/user_1_123456789.jpg'
      );
    }, { timeout: 3000 });
  });

  it('handles upload error correctly', async () => {
    const mockError = {
      response: {
        data: {
          message: 'Invalid aspect ratio',
        },
      },
    };

    (apiService.uploadProfileImage as jest.Mock).mockRejectedValue(mockError);

    render(
      <ImageUpload
        userId={1}
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    const fileInput = screen.getByRole('button').querySelector('input[type="file"]') as HTMLInputElement;
    
    const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    fireEvent.change(fileInput, { target: { files: [validFile] } });

    await waitFor(() => {
      expect(mockOnError).toHaveBeenCalledWith('Invalid aspect ratio');
      expect(screen.getByText('Invalid aspect ratio')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('disables upload when disabled prop is true', () => {
    render(
      <ImageUpload
        userId={1}
        disabled={true}
        onImageUploaded={mockOnImageUploaded}
        onError={mockOnError}
      />
    );

    const uploadArea = screen.getByRole('button');
    expect(uploadArea).toHaveClass('opacity-50', 'cursor-not-allowed');
    
    const fileInput = uploadArea.querySelector('input[type="file"]') as HTMLInputElement;
    expect(fileInput).toBeDisabled();
  });
});
