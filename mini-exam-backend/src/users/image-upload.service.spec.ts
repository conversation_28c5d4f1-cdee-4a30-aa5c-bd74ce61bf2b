import { Test, TestingModule } from '@nestjs/testing';
import { ImageUploadService } from './image-upload.service';
import * as fs from 'fs';
import * as path from 'path';

describe('ImageUploadService', () => {
  let service: ImageUploadService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ImageUploadService],
    }).compile();

    service = module.get<ImageUploadService>(ImageUploadService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processAndValidateImage', () => {
    it('should reject images with wrong aspect ratio', async () => {
      // Create a mock buffer for a square image (1:1 ratio)
      const mockBuffer = Buffer.from('mock-image-data');
      
      // Mock sharp to return square dimensions
      jest.doMock('sharp', () => {
        return jest.fn(() => ({
          metadata: jest.fn().mockResolvedValue({
            width: 300,
            height: 300, // Square image
          }),
        }));
      });

      const result = await service.processAndValidateImage(
        mockBuffer,
        'test.jpg',
        1
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Image must have aspect ratio of 3:4 or 4:6 (portrait orientation)');
    });

    it('should generate correct image URL', () => {
      const filename = 'user_1_123456789.jpg';
      const url = service.getImageUrl(filename);
      expect(url).toBe('/uploads/profile-images/user_1_123456789.jpg');
    });

    it('should generate correct image path', () => {
      const filename = 'user_1_123456789.jpg';
      const imagePath = service.getImagePath(filename);
      expect(imagePath).toContain('uploads/profile-images/user_1_123456789.jpg');
    });
  });

  describe('deleteImage', () => {
    it('should handle deletion of non-existent file gracefully', async () => {
      // This should not throw an error
      await expect(service.deleteImage('non-existent.jpg')).resolves.not.toThrow();
    });

    it('should handle empty filename gracefully', async () => {
      // This should not throw an error
      await expect(service.deleteImage('')).resolves.not.toThrow();
    });
  });
});
