import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { ImageUploadService } from './image-upload.service';
import { BadRequestException } from '@nestjs/common';
import { UserRole } from '../entities/user.entity';

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: UsersService;
  let imageUploadService: ImageUploadService;

  const mockUsersService = {
    findOne: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
    findAll: jest.fn(),
    remove: jest.fn(),
  };

  const mockImageUploadService = {
    processAndValidateImage: jest.fn(),
    deleteImage: jest.fn(),
    getImageUrl: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: ImageUploadService,
          useValue: mockImageUploadService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
    imageUploadService = module.get<ImageUploadService>(ImageUploadService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('uploadProfileImage', () => {
    const mockFile = {
      buffer: Buffer.from('mock-image-data'),
      originalname: 'test.jpg',
      mimetype: 'image/jpeg',
      size: 1024,
    } as Express.Multer.File;

    const mockUser = {
      id: 1,
      username: 'testuser',
      name: 'Test',
      lastName: 'User',
      role: UserRole.USER,
      profileImage: null,
    };

    const mockRequest = {
      user: {
        userId: 1,
        role: UserRole.USER,
      },
    };

    it('should throw BadRequestException when no file is provided', async () => {
      await expect(
        controller.uploadProfileImage(1, undefined as any, mockRequest)
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when user tries to upload for another user', async () => {
      const unauthorizedRequest = {
        user: {
          userId: 2, // Different user ID
          role: UserRole.USER,
        },
      };

      await expect(
        controller.uploadProfileImage(1, mockFile, unauthorizedRequest)
      ).rejects.toThrow(BadRequestException);
    });

    it('should allow admin to upload for any user', async () => {
      const adminRequest = {
        user: {
          userId: 2,
          role: UserRole.ADMIN,
        },
      };

      mockImageUploadService.processAndValidateImage.mockResolvedValue({
        isValid: true,
        errors: [],
        processedImagePath: 'user_1_123456789.jpg',
      });

      mockUsersService.findOne.mockResolvedValue(mockUser);
      mockUsersService.update.mockResolvedValue(mockUser);
      mockImageUploadService.getImageUrl.mockReturnValue('/uploads/profile-images/user_1_123456789.jpg');

      const result = await controller.uploadProfileImage(1, mockFile, adminRequest);

      expect(result.message).toBe('Profile image uploaded successfully');
      expect(result.imageUrl).toBe('/uploads/profile-images/user_1_123456789.jpg');
    });

    it('should allow user to upload for their own profile', async () => {
      mockImageUploadService.processAndValidateImage.mockResolvedValue({
        isValid: true,
        errors: [],
        processedImagePath: 'user_1_123456789.jpg',
      });

      mockUsersService.findOne.mockResolvedValue(mockUser);
      mockUsersService.update.mockResolvedValue(mockUser);
      mockImageUploadService.getImageUrl.mockReturnValue('/uploads/profile-images/user_1_123456789.jpg');

      const result = await controller.uploadProfileImage(1, mockFile, mockRequest);

      expect(result.message).toBe('Profile image uploaded successfully');
      expect(result.imageUrl).toBe('/uploads/profile-images/user_1_123456789.jpg');
      expect(mockImageUploadService.processAndValidateImage).toHaveBeenCalledWith(
        mockFile.buffer,
        mockFile.originalname,
        1
      );
    });

    it('should delete old profile image when uploading new one', async () => {
      const userWithImage = {
        ...mockUser,
        profileImage: 'old_image.jpg',
      };

      mockImageUploadService.processAndValidateImage.mockResolvedValue({
        isValid: true,
        errors: [],
        processedImagePath: 'user_1_123456789.jpg',
      });

      mockUsersService.findOne.mockResolvedValue(userWithImage);
      mockUsersService.update.mockResolvedValue(userWithImage);
      mockImageUploadService.getImageUrl.mockReturnValue('/uploads/profile-images/user_1_123456789.jpg');

      await controller.uploadProfileImage(1, mockFile, mockRequest);

      expect(mockImageUploadService.deleteImage).toHaveBeenCalledWith('old_image.jpg');
    });

    it('should throw BadRequestException when image validation fails', async () => {
      mockImageUploadService.processAndValidateImage.mockResolvedValue({
        isValid: false,
        errors: ['Invalid aspect ratio'],
      });

      await expect(
        controller.uploadProfileImage(1, mockFile, mockRequest)
      ).rejects.toThrow(BadRequestException);
    });
  });
});
