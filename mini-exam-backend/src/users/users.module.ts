import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { User } from '../entities/user.entity';
import { ImageUploadService } from './image-upload.service';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  providers: [UsersService, ImageUploadService],
  controllers: [UsersController],
  exports: [UsersService],
})
export class UsersModule {}
