import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiConsumes, ApiParam } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { UserRole } from '../entities/user.entity';
import { RescuePasswordChangeDto } from './dto/rescue-password-change.dto';
import { ImageUploadService } from './image-upload.service';

@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly imageUploadService: ImageUploadService,
  ) {}

  @Post('rescue-password-change')
  rescuePasswordChange(@Body() body: RescuePasswordChangeDto) {
    return this.usersService.rescuePasswordChange(
      body.username,
      body.newPassword,
      body.secretKey,
    );
  }
  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  findAll() {
    return this.usersService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.usersService.update(id, updateUserDto);
  }

  @Post(':id/profile-image')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('image', {
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit for upload (will be processed down to 70KB)
    },
    fileFilter: (req, file, callback) => {
      if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
        return callback(new BadRequestException('Only image files are allowed'), false);
      }
      callback(null, true);
    },
  }))
  @ApiOperation({
    summary: 'Upload profile image for user',
    description: 'Upload and process a profile image for a user. Image will be automatically resized and optimized. Requires 3:4 or 4:6 aspect ratio (portrait orientation).'
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({ name: 'id', description: 'User ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Profile image uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Profile image uploaded successfully' },
        imageUrl: { type: 'string', example: '/uploads/profile-images/user_1_123456789.jpg' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid image file or validation failed' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only upload for own profile unless admin' })
  async uploadProfileImage(
    @Param('id', ParseIntPipe) id: number,
    @UploadedFile() file: Express.Multer.File,
    @Request() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('No image file provided');
    }

    // Check if user can upload image for this user ID
    const isAdmin = req.user.role === UserRole.ADMIN;
    const isOwnProfile = req.user.userId === id;

    if (!isAdmin && !isOwnProfile) {
      throw new BadRequestException('You can only upload images for your own profile');
    }

    // Process and validate the image
    const result = await this.imageUploadService.processAndValidateImage(
      file.buffer,
      file.originalname,
      id,
    );

    if (!result.isValid) {
      throw new BadRequestException(result.errors.join(', '));
    }

    // Update user's profile image
    const user = await this.usersService.findOne(id);

    // Delete old profile image if exists
    if (user.profileImage) {
      await this.imageUploadService.deleteImage(user.profileImage);
    }

    // Update user with new profile image
    await this.usersService.update(id, { profileImage: result.processedImagePath });

    return {
      message: 'Profile image uploaded successfully',
      imageUrl: this.imageUploadService.getImageUrl(result.processedImagePath!),
    };
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.remove(id);
  }
}
