import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Question } from '../entities/question.entity';
import { Exam } from '../entities/exam.entity';
import { Course } from '../entities/course.entity';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';

@Injectable()
export class QuestionsService {
  constructor(
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    @InjectRepository(Exam)
    private examRepository: Repository<Exam>,
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
  ) {}

  async create(createQuestionDto: CreateQuestionDto): Promise<Question> {
    // Verify exam exists
    const exam = await this.examRepository.findOne({
      where: { id: createQuestionDto.examId },
    });

    if (!exam) {
      throw new NotFoundException(`Exam with ID ${createQuestionDto.examId} not found`);
    }

    // Verify course exists
    const course = await this.courseRepository.findOne({
      where: { id: createQuestionDto.courseId },
    });

    if (!course) {
      throw new NotFoundException(`Course with ID ${createQuestionDto.courseId} not found`);
    }

    // Check if question number already exists for this exam
    const existingQuestion = await this.questionRepository.findOne({
      where: { 
        examId: createQuestionDto.examId,
        questionNumber: createQuestionDto.questionNumber 
      },
    });

    if (existingQuestion) {
      throw new BadRequestException(
        `Question number ${createQuestionDto.questionNumber} already exists for this exam`
      );
    }

    const question = this.questionRepository.create(createQuestionDto);
    return this.questionRepository.save(question);
  }

  async findAll(): Promise<Question[]> {
    return this.questionRepository.find({
      relations: ['exam', 'course'],
      order: { examId: 'ASC', questionNumber: 'ASC' },
    });
  }

  async findByExam(examId: number): Promise<Question[]> {
    return this.questionRepository.find({
      where: { examId },
      relations: ['course'],
      order: { questionNumber: 'ASC' },
    });
  }

  async findOne(id: number): Promise<Question> {
    const question = await this.questionRepository.findOne({
      where: { id },
      relations: ['exam', 'course'],
    });

    if (!question) {
      throw new NotFoundException(`Question with ID "${id}" not found`);
    }

    return question;
  }

  async update(id: number, updateQuestionDto: UpdateQuestionDto): Promise<Question> {
    const question = await this.findOne(id);

    // If updating question number, check for conflicts
    if (updateQuestionDto.questionNumber && updateQuestionDto.questionNumber !== question.questionNumber) {
      const existingQuestion = await this.questionRepository.findOne({
        where: { 
          examId: question.examId,
          questionNumber: updateQuestionDto.questionNumber 
        },
      });

      if (existingQuestion && existingQuestion.id !== id) {
        throw new BadRequestException(
          `Question number ${updateQuestionDto.questionNumber} already exists for this exam`
        );
      }
    }

    Object.assign(question, updateQuestionDto);
    return this.questionRepository.save(question);
  }

  async remove(id: number): Promise<void> {
    const question = await this.findOne(id);
    await this.questionRepository.remove(question);
  }

  async removeByExam(examId: number): Promise<void> {
    await this.questionRepository.delete({ examId });
  }
}
