import { IsString, IsNotEmpty, IsInt, IsEnum, Min, IsArray, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AnswerOption, QuestionType } from '../../entities/question.entity';

export class CreateQuestionDto {
  @ApiProperty({ description: 'Exam ID' })
  @IsInt()
  examId: number;

  @ApiProperty({ description: 'Course ID' })
  @IsInt()
  courseId: number;

  @ApiProperty({ description: 'Question number in the exam', minimum: 1 })
  @IsInt()
  @Min(1)
  questionNumber: number;

  @ApiProperty({ description: 'Question text' })
  @IsString()
  @IsNotEmpty()
  questionText: string;

  @ApiProperty({ 
    description: 'Answer options (exactly 4 options)',
    type: [String],
    example: ['گزینه الف', 'گزینه ب', 'گزینه ج', 'گزینه د']
  })
  @IsArray()
  @ArrayMinSize(4)
  @ArrayMaxSize(4)
  options: string[];

  @ApiProperty({ 
    description: 'Correct answer option',
    enum: AnswerOption,
    example: AnswerOption.A
  })
  @IsEnum(AnswerOption)
  correctAnswer: AnswerOption;

  @ApiProperty({ 
    description: 'Question type',
    enum: QuestionType,
    default: QuestionType.MULTIPLE_CHOICE
  })
  @IsEnum(QuestionType)
  type?: QuestionType;
}
