import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, JoinC<PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsString, IsNotEmpty, IsInt, IsEnum, Min, IsArray } from 'class-validator';
import { Exam } from './exam.entity';
import { Course } from './course.entity';

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
}

export enum AnswerOption {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}

@Entity('questions')
export class Question {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'exam_id' })
  examId: number;

  @ManyToOne(() => Exam, exam => exam.questions)
  @JoinColumn({ name: 'exam_id' })
  exam: Exam;

  @Column({ name: 'course_id' })
  courseId: number;

  @ManyToOne(() => Course)
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @Column({ name: 'question_number' })
  @IsInt()
  @Min(1)
  questionNumber: number;

  @Column({ type: 'text', name: 'question_text' })
  @IsString()
  @IsNotEmpty()
  questionText: string;

  @Column({ type: 'jsonb' })
  @IsArray()
  options: string[];

  @Column({ 
    type: 'enum',
    enum: AnswerOption,
    name: 'correct_answer'
  })
  @IsEnum(AnswerOption)
  correctAnswer: AnswerOption;

  @Column({
    type: 'enum',
    enum: QuestionType,
    default: QuestionType.MULTIPLE_CHOICE,
  })
  @IsEnum(QuestionType)
  type: QuestionType;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
