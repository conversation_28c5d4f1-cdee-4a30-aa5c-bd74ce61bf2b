import { <PERSON>ti<PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsString, IsEnum, MinLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Session } from './session.entity';

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}

@Entity('users')
export class User {
  @ApiProperty({ description: 'User ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'Unique username', example: 'john_doe_1234' })
  @Column({ unique: true })
  @IsString()
  @MinLength(3)
  username: string;

  @ApiProperty({ description: 'User password (hashed)', writeOnly: true })
  @Column()
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ description: 'User first name', example: '<PERSON>', required: false })
  @Column({ nullable: true })
  @IsString()
  @MinLength(2)
  name: string;

  @ApiProperty({ description: 'User last name', example: 'Doe', required: false })
  @Column({ nullable: true })
  @IsString()
  @MinLength(2)
  lastName: string;

  @ApiProperty({
    description: 'Profile image filename',
    example: 'user_1_123456789.jpg',
    required: false,
    nullable: true
  })
  @Column({ nullable: true })
  @IsString()
  profileImage: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.USER,
    default: UserRole.USER
  })
  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  @IsEnum(UserRole)
  role: UserRole;

  @ApiProperty({ description: 'User exam sessions', type: () => [Session] })
  @OneToMany(() => Session, session => session.user)
  sessions: Session[];

  @ApiProperty({ description: 'Account creation date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
