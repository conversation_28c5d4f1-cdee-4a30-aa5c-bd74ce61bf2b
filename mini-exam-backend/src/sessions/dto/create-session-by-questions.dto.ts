import { IsInt, <PERSON>Array, Val<PERSON>teNested, IsString, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class QuestionAnswerDto {
  @ApiProperty({ description: 'Question ID' })
  @IsInt()
  questionId: number;

  @ApiProperty({ 
    description: 'Selected answer option',
    example: 'A',
    required: false
  })
  @IsOptional()
  @IsString()
  selectedAnswer?: string; // 'A', 'B', 'C', 'D', or null for unanswered
}

export class CreateSessionByQuestionsDto {
  @ApiProperty({ description: 'Exam ID' })
  @IsInt()
  examId: number;

  @ApiProperty({ 
    description: 'List of answers for each question',
    type: [QuestionAnswerDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestionAnswerDto)
  questionAnswers: QuestionAnswerDto[];
}
